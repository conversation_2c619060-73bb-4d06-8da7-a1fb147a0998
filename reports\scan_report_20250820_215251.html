
        <!DOCTYPE html>
        <html>
        <head>
            <title>SOC Automation Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; }
                .summary { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
                .critical { color: red; font-weight: bold; }
                .warning { color: orange; }
                .info { color: blue; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>SOC Automation Report</h1>
                <p>Generated: 2025-08-20T21:52:51.591396</p>
                <p>Report Type: network_scan</p>
            </div>
            <div class="summary">
                <h2>Summary</h2>
                <p>Targets Scanned: 2</p>
                <p>Reachable Hosts: 2</p>
                <p>Total Open Ports: 6</p>
            </div>
            <div class="details">
                <h2>Detailed Results</h2>
                <table>
                    <tr>
                        <th>Target</th>
                        <th>Reachable</th>
                        <th>Open Ports</th>
                        <th>Services</th>
                    </tr>
        
                    <tr>
                        <td>*******</td>
                        <td>YES</td>
                        <td>53, 443</td>
                        <td></td>
                    </tr>
            
                    <tr>
                        <td>*******</td>
                        <td>YES</td>
                        <td>53, 80, 443, 8080</td>
                        <td>80:HTTP/1.1 301 Moved Permanently

Server: cloudflare..., 443:HTTP/1.1 400 Bad Request

Server: cloudflare

Date..., 8080:HTTP/1.1 301 Moved Permanently

Server: cloudflare...</td>
                    </tr>
            
                </table>
            </div>
        </body>
        </html>
        