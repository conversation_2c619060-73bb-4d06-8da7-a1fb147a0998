Original Project Lead:
----------------------
<PERSON><PERSON><PERSON> <<EMAIL>>

Current Project Lead:
---------------------
<PERSON><PERSON><PERSON> <<EMAIL>>

Contributors in alphabetical order:
-----------------------------------
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON> <PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
dvogel <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
gfxmonk <<EMAIL>>
Gora Khargosh <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON>k<PERSON><PERSON>inský <<EMAIL>>
Malthe Borch <<EMAIL>>
<PERSON> Kreichgauer <<EMAIL>>
Martin Kreichgauer <<EMAIL>>
Mike Lundy <<EMAIL>>
Nicholas Hairs <<EMAIL>>
Raymond Hettinger <<EMAIL>>
Roman Ovchinnikov <<EMAIL>>
Rotem Yaari <<EMAIL>>
Ryan Kelly <<EMAIL>>
Senko Rasic <<EMAIL>>
Senko Rašić <<EMAIL>>
Shane Hathaway <<EMAIL>>
Simon Pantzare <<EMAIL>>
Simon Pantzare <<EMAIL>>
Steven Samuel Cole <<EMAIL>>
Stéphane Klein <<EMAIL>>
Thomas Guest <<EMAIL>>
Thomas Heller <<EMAIL>>
Tim Cuthbertson <<EMAIL>>
Todd Whiteman <<EMAIL>>
Will McGugan <<EMAIL>>
Yesudeep Mangalapilly <<EMAIL>>
Yesudeep Mangalapilly <<EMAIL>>

We would like to thank these individuals for ideas:
---------------------------------------------------
Tim Golden               <<EMAIL>>
Sebastien Martini        <<EMAIL>>

Initially we used the flask theme for the documentation which was written by
----------------------------------------------------------------------------
Armin Ronacher           <<EMAIL>>


Watchdog also includes open source libraries or adapted code
from the following projects:

- MacFSEvents - https://github.com/malthe/macfsevents
- watch_directory.py - http://timgolden.me.uk/python/downloads/watch_directory.py
- pyinotify - https://github.com/seb-m/pyinotify
- fsmonitor - https://github.com/shaurz/fsmonitor
- echo - http://wordaligned.org/articles/echo
- Lukáš Lalinský's ordered set queue implementation:
  https://stackoverflow.com/questions/1581895/how-check-if-a-task-is-already-in-python-queue
- Armin Ronacher's flask-sphinx-themes for the documentation:
  https://github.com/mitsuhiko/flask-sphinx-themes
- pyfilesystem - https://github.com/PyFilesystem/pyfilesystem
- get_FILE_NOTIFY_INFORMATION - http://blog.gmane.org/gmane.comp.python.ctypes/month=20070901
